#!/usr/bin/env python3
"""
Check the output stream for processed messages.
"""
import asyncio
import sys
import redis.asyncio as redis

async def check_output_stream():
    """Check the status of the output stream."""
    r = redis.Redis.from_url("redis://localhost:6379/0", decode_responses=True)
    
    output_stream = "mt:active:out"
    
    print("=== Output Stream Status ===")
    
    try:
        # Get stream length
        stream_length = await r.xlen(output_stream)
        print(f"Output stream length: {stream_length}")
        
        if stream_length > 0:
            # Get some sample messages
            messages = await r.xrange(output_stream, count=5)
            print(f"Sample messages (first 5):")
            for msg_id, msg_data in messages:
                print(f"  ID: {msg_id}")
                print(f"  Data: {msg_data}")
                print()
        
        # Check if there are any consumer groups
        try:
            groups = await r.xinfo_groups(output_stream)
            print(f"Consumer groups: {len(groups)}")
            for group_info in groups:
                print(f"  Group: {group_info['name']}")
                print(f"  Consumers: {group_info['consumers']}")
                print(f"  Pending: {group_info['pending']}")
        except Exception as e:
            print(f"No consumer groups or error: {e}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    await r.close()

if __name__ == "__main__":
    asyncio.run(check_output_stream())
