[tool.poetry]
name = "data-prepper"
version = "0.1.0"
description = "Data processing tool for MT training"
authors = ["<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.11"
redis = "^6.2.0"
pydantic = "^2.11.7"
loguru = "^0.7.3"
prometheus-client = "^0.22.1"
typer = "^0.16.0"
pyyaml = "^6.0.2"


[tool.setuptools]
package-dir = {"" = "src"}
packages = ["dataprepper", "dataprepper.tasks"]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
