#!/usr/bin/env python3
"""
Test script to verify message flow between master and workers.
This helps debug why workers might not be consuming messages.
"""
import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, 'src')

from dataprepper.broker import <PERSON>roker
from loguru import logger

async def test_message_flow():
    """Test basic message flow through Redis streams."""
    broker = Broker()
    stream = "mt:active:in"
    group = "cg1"  # Same as workers use
    consumer = "test_consumer"
    
    logger.info("=== Testing Message Flow ===")
    
    # 1. Check initial stream length
    initial_length = await broker.get_stream_length(stream)
    logger.info(f"Initial stream length: {initial_length}")
    
    # 2. Create consumer group
    success = await broker.create_consumer_group(stream, group)
    logger.info(f"Consumer group creation: {'success' if success else 'already exists'}")
    
    # 3. Push a test message
    test_message = {
        "task": "test_task",
        "sentence_id": 1,
        "src": "Hello world",
        "tgt": "Hola mundo"
    }
    
    await broker.push_messages(stream, [test_message])
    logger.info("Pushed test message")
    
    # 4. Check stream length after push
    after_push_length = await broker.get_stream_length(stream)
    logger.info(f"Stream length after push: {after_push_length}")
    
    # 5. Try to pull messages
    messages = await broker.pull_messages(
        group=group,
        consumer=consumer,
        stream=stream,
        count=1,
        block_ms=1000
    )
    
    logger.info(f"Pulled {len(messages)} messages")
    for msg in messages:
        logger.info(f"Message ID: {msg.id}, Data: {msg.data}")
    
    # 6. Acknowledge messages if any
    if messages:
        ack_ids = [msg.id for msg in messages]
        ack_count = await broker.acknowledge_messages(stream, group, ack_ids)
        logger.info(f"Acknowledged {ack_count} messages")
    
    # 7. Final stream length
    final_length = await broker.get_stream_length(stream)
    logger.info(f"Final stream length: {final_length}")
    
    logger.info("=== Test Complete ===")

if __name__ == "__main__":
    asyncio.run(test_message_flow())
