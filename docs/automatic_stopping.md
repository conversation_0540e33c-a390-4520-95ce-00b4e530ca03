# Automatic Process Stopping

This document describes the automatic stopping mechanisms implemented in data-prepper for master, worker, and writer processes.

## Overview

Data-prepper now includes automatic stopping mechanisms to ensure that all processes terminate gracefully when their work is complete or when they have been idle for too long. This prevents processes from running indefinitely and consuming resources unnecessarily.

## Process Types and Stopping Behavior

### 1. Master Process

**Behavior**: The master process automatically stops once all files have been read and pushed to the input stream.

**Implementation**:
- The master process reads all configured input files for enabled tasks
- After processing all files, it waits for the input stream to drain (become empty)
- Once the stream is empty, the master process terminates automatically
- Enhanced logging provides clear indication of completion

**Configuration**: No configuration needed - this is automatic based on file completion.

### 2. Worker Processes

**Behavior**: Worker processes automatically stop after the input queue has been empty for a configurable timeout period (default: 5 minutes).

**Implementation**:
- Workers track the last time they received a message from the input stream
- If no messages are received for the configured timeout period, the worker stops automatically
- The idle timer resets whenever new messages are received
- Workers log their idle status periodically for monitoring

**Configuration**: Set `timeouts.worker_idle_timeout` in `configs/master.yaml` (in seconds).

### 3. Writer Processes

**Behavior**: Writer processes automatically stop after the output queue has been empty for a configurable timeout period (default: 5 minutes).

**Implementation**:
- Individual writers track idle time and stop when no messages are received for the timeout period
- The WriterManager also monitors the output stream and stops all writers when empty for the timeout period
- The idle timer resets whenever new messages are received
- Writers flush any remaining buffered results before stopping

**Configuration**: Set `timeouts.writer_idle_timeout` in `configs/master.yaml` (in seconds).

## Configuration

Add timeout settings to your `configs/master.yaml` file:

```yaml
src_lang: en
tgt_lang: es

# Timeout configuration for automatic process stopping
timeouts:
  worker_idle_timeout: 300    # Workers stop after 5 minutes of no messages
  writer_idle_timeout: 300    # Writers stop after 5 minutes of no messages

tasks:
  # ... your task configurations
```

### Configuration Options

- `worker_idle_timeout`: Time in seconds that workers wait for new messages before stopping (default: 300)
- `writer_idle_timeout`: Time in seconds that writers wait for new messages before stopping (default: 300)

## Logging

The automatic stopping mechanisms provide detailed logging:

### Master Process
```
INFO: All tasks processed, waiting for input stream to drain...
INFO: All tasks completed, input stream drained - master process stopping automatically
```

### Worker Processes
```
INFO: Starting worker loop for task: char_count (will auto-stop after 300s of inactivity)
DEBUG: Worker for task char_count idle for 60s
INFO: Worker for task char_count stopping due to 300s idle timeout
INFO: Worker loop stopped for task: char_count
```

### Writer Processes
```
INFO: Starting writer loop for task: char_count (will auto-stop after 300s of inactivity)
DEBUG: Writer for task char_count idle for 60s
INFO: Writer for task char_count stopping due to 300s idle timeout
INFO: Writer loop stopped for task: char_count
```

## Benefits

1. **Resource Efficiency**: Processes don't consume resources indefinitely
2. **Automatic Cleanup**: No manual intervention needed to stop processes
3. **Graceful Shutdown**: All processes complete their current work before stopping
4. **Configurable Timeouts**: Adjust timeouts based on your processing requirements
5. **Clear Monitoring**: Detailed logging helps track process lifecycle

## Best Practices

1. **Set Appropriate Timeouts**: Consider your data processing patterns when setting timeouts
   - For batch processing: Shorter timeouts (2-5 minutes) are usually sufficient
   - For streaming/continuous processing: Longer timeouts may be needed

2. **Monitor Logs**: Watch for timeout messages to ensure processes are stopping as expected

3. **Test Configuration**: Use the provided test script to verify timeout behavior:
   ```bash
   python test_auto_stopping.py
   ```

4. **Adjust for Your Workload**: If processes are stopping too early or too late, adjust the timeout values

## Troubleshooting

### Workers/Writers Stopping Too Early
- Increase the respective timeout values in the configuration
- Check if there are delays in message processing that might cause apparent "idle" periods

### Workers/Writers Not Stopping
- Verify the configuration is loaded correctly
- Check logs for any errors in the timeout logic
- Ensure the Redis streams are actually empty

### Master Process Not Stopping
- Check that all input files exist and are readable
- Verify that the stream draining logic is working correctly
- Look for any errors in file processing

## Implementation Details

The automatic stopping is implemented using:
- Time-based idle tracking for workers and writers
- Stream monitoring for completion detection
- Graceful shutdown with proper resource cleanup
- Configuration-driven timeout values
- Comprehensive logging for monitoring and debugging
