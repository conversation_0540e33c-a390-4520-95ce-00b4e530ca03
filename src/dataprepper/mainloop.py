# src/dataprepper/mainloop.py
import asyncio
import itertools
from pathlib import Path
from typing import As<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dict, Any

from loguru import logger

from .broker import Broker
from .conf import load_master_cfg, TaskMeta, MasterCfg
from .metrics import PUSH_RATE, QUEUE_DEPTH
from .writer import run_writers

# Configuration constants
LOW_WM, HIGH_WM = 5_000, 50_000
STREAM_IN = "mt:active:in"
BACKPRESSURE_SLEEP = 0.5
DRAIN_CHECK_INTERVAL = 1.0


class DataProcessor:

    def __init__(self):
        self.broker = Broker()
        self.config = load_master_cfg()

    async def process_all_tasks(self) -> None:
        """Process all enabled tasks in the configuration."""
        enabled_tasks = self._get_enabled_tasks()

        if not enabled_tasks:
            logger.warning("No enabled tasks found in configuration")
            return

        logger.info(f"Starting processing for {len(enabled_tasks)} enabled tasks: {list(enabled_tasks.keys())}")

        writer_task = asyncio.create_task(run_writers(), name="writers")
        logger.info("Started writer processes")

        try:
            # Process all tasks sequentially
            for task_name, task_meta in enabled_tasks.items():
                logger.info(f"Processing task: {task_name}")
                await self._process_single_task(task_name, task_meta)
                logger.info(f"Completed processing task: {task_name}")

            logger.info("All tasks processed, waiting for input stream to drain...")
            await self._wait_for_stream_drain()
            logger.info("All tasks completed, input stream drained - master process stopping automatically")

        finally:
            if not writer_task.done():
                logger.info("Waiting for writers to complete...")
                try:
                    await asyncio.wait_for(writer_task, timeout=30)
                except asyncio.TimeoutError:
                    logger.warning("Writers did not complete within timeout, cancelling...")
                    writer_task.cancel()
                    try:
                        await writer_task
                    except asyncio.CancelledError:
                        pass


    def _get_enabled_tasks(self) -> Dict[str, TaskMeta]:
        """Get all enabled tasks from configuration."""
        return {
            name: meta
            for name, meta in self.config.tasks.items()
            if meta.enabled
        }


    async def _process_single_task(self, task_name: str, task_meta: TaskMeta) -> None:
        file_paths = self._get_task_file_paths(task_meta)
        sentence_counter = itertools.count()

        async for src_line, tgt_line in self._read_file_pairs(file_paths):
            await self._handle_backpressure(task_name)

            message = self._create_message(
                task_name,
                next(sentence_counter),
                src_line,
                tgt_line
            )

            await self._push_message(task_name, message)


    def _get_task_file_paths(self, task_meta: TaskMeta) -> Tuple[Path, Path]:
        """Get source and target file paths for a task."""
        src_path = Path(f"{task_meta.corpus_prefix}.{self.config.src_lang}")
        tgt_path = Path(f"{task_meta.corpus_prefix}.{self.config.tgt_lang}")
        return src_path, tgt_path


    async def _read_file_pairs(
        self,
        file_paths: Tuple[Path, Path]
    ) -> AsyncGenerator[Tuple[str, str], None]:
        """Read source and target files line by line asynchronously."""
        src_path, tgt_path = file_paths
        loop = asyncio.get_running_loop()

        def _read_files():
            with src_path.open(encoding="utf-8") as src_file, \
                 tgt_path.open(encoding="utf-8") as tgt_file:
                for src_line, tgt_line in zip(src_file, tgt_file):
                    yield src_line.rstrip("\n"), tgt_line.rstrip("\n")

        file_pairs = await loop.run_in_executor(None, list, _read_files())
        for pair in file_pairs:
            yield pair


    async def _handle_backpressure(self, task_name: str) -> None:
        queue_length = await self.broker.get_stream_length(STREAM_IN)
        QUEUE_DEPTH.labels(task=task_name).set(queue_length)

        if queue_length > HIGH_WM:
            await asyncio.sleep(BACKPRESSURE_SLEEP)


    def _create_message(
        self,
        task_name: str,
        sentence_id: int,
        src_text: str,
        tgt_text: str
    ) -> Dict[str, Any]:
        """Create a message dictionary for the stream."""
        return {
            "task": task_name,
            "sentence_id": sentence_id,
            "src": src_text,
            "tgt": tgt_text,
        }


    async def _push_message(self, task_name: str, message: Dict[str, Any]) -> None:
        logger.debug(f"Pushing message for task {task_name}: {message}")
        await self.broker.push_messages(STREAM_IN, [message])
        PUSH_RATE.labels(task=task_name).inc()
        logger.debug(f"Successfully pushed message for task {task_name}")


    async def _wait_for_stream_drain(self) -> None:
        while True:
            queue_length = await self.broker.get_stream_length(STREAM_IN)
            if queue_length == 0:
                break
            await asyncio.sleep(DRAIN_CHECK_INTERVAL)


async def run_main() -> None:
    """Main entry point for the data processing pipeline."""
    processor = DataProcessor()
    await processor.process_all_tasks()

