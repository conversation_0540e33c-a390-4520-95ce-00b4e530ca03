# src/dataprepper/tasks/__init__.py
"""
Registry of task processors.
Each module exposes a `process(data: dict) -> dict` function.
"""

from importlib import import_module
from typing import Callable, Dict

_REGISTRY: Dict[str, Callable[[dict], dict]] = {}


def _register(name: str):
    def decorator(fn):
        _REGISTRY[name] = fn
        return fn

    return decorator


def get(task_name: str) -> Callable[[dict], dict]:
    if task_name not in _REGISTRY:
        # Lazy import on first use
        import_module(f"dataprepper.tasks.{task_name}")
    return _REGISTRY[task_name]