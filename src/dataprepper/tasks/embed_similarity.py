# src/dataprepper/tasks/cosine_similarity.py
"""
Tiny self-contained similarity demo.
Replace with real sentence-transformers model when GPUs are present.
"""
import math
import random
from . import _register


def _toy_embed(text: str, dim: int = 64) -> list[float]:
    random.seed(hash(text) % (2**32))
    return [random.random() for _ in range(dim)]


def _cosine(a: list[float], b: list[float]) -> float:
    dot = sum(x * y for x, y in zip(a, b))
    na = math.sqrt(sum(x * x for x in a))
    nb = math.sqrt(sum(y * y for y in b))
    return dot / (na * nb + 1e-9)


@_register("embed_similarity")
def process(msg: dict) -> dict:
    src_vec = _toy_embed(msg["src"])
    tgt_vec = _toy_embed(msg["tgt"])
    score = _cosine(src_vec, tgt_vec)
    return {
        "sentence_id": msg["sentence_id"],
        "score": score,
    }