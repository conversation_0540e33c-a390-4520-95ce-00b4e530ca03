# src/dataprepper/metrics.py
"""
Metrics collection and monitoring for the dataprepper application.
Uses Prometheus client for metrics collection and HTTP server for exposure.
"""
from typing import Optional

from loguru import logger
from prometheus_client import Counter, Gauge, start_http_server


class MetricsRegistry:

    def __init__(self):
        self._setup_metrics()

    def _setup_metrics(self) -> None:
        # Processing metrics
        self.messages_processed = Counter(
            "dataprepper_messages_processed_total",
            "Total number of messages processed",
            ["task", "node_id"],
        )

        # Queue metrics
        self.queue_depth = Gauge(
            "dataprepper_queue_depth",
            "Current depth of the input queue",
            ["task"]
        )

        # Throughput metrics
        self.push_rate = Counter(
            "dataprepper_messages_pushed_total",
            "Total number of messages pushed to streams",
            ["task"]
        )

        # Error metrics
        self.processing_errors = Counter(
            "dataprepper_processing_errors_total",
            "Total number of processing errors",
            ["task", "error_type"]
        )


class MetricsServer:

    def __init__(self):
        self._server_started = False
        self._port: Optional[int] = None

    def start(self, port: int = 8000) -> None:
        """
        Start the metrics HTTP server.

        Args:
            port: Port number for the metrics server
        """
        if self._server_started:
            logger.warning(f"Metrics server already running on port {self._port}")
            return

        try:
            start_http_server(port)
            self._server_started = True
            self._port = port
            logger.info(f"Metrics server started on port {port}")
        except Exception as e:
            logger.error(f"Failed to start metrics server on port {port}: {e}")
            raise

    def is_running(self) -> bool:
        """Check if the metrics server is running."""
        return self._server_started

    @property
    def port(self) -> Optional[int]:
        """Get the port the metrics server is running on."""
        return self._port


# Global metrics registry
_metrics_registry = MetricsRegistry()

# Export metrics for backward compatibility and easy access
MSG_PROCESSED = _metrics_registry.messages_processed
QUEUE_DEPTH = _metrics_registry.queue_depth
PUSH_RATE = _metrics_registry.push_rate
PROCESSING_ERRORS = _metrics_registry.processing_errors


# Legacy function for backward compatibility
def boot_metrics(port: int = 8000) -> None:
    """
    Legacy function to start metrics server.
    Use MetricsServer.start() instead.
    """
    server = MetricsServer()
    server.start(port)