# src/dataprepper/conf.py
from pathlib import Path
from typing import Dict, List, Literal

import yaml
from pydantic import BaseModel, Field


class TaskMeta(BaseModel):
    enabled: bool = True
    corpus_prefix: str
    output_prefix: str
    io_format: str
    input_stream: str
    output_stream: str


class TimeoutConfig(BaseModel):
    """Configuration for process timeout settings."""
    worker_idle_timeout: int = Field(default=300, description="Worker idle timeout in seconds (default: 5 minutes)")
    writer_idle_timeout: int = Field(default=300, description="Writer idle timeout in seconds (default: 5 minutes)")


class MasterCfg(BaseModel):
    src_lang: str
    tgt_lang: str
    tasks: Dict[str, TaskMeta]
    timeouts: TimeoutConfig = Field(default_factory=TimeoutConfig)


class TaskCfg(BaseModel):
    enabled: bool = True
    num_workers: int
    worker_type: Literal["cpu", "gpu"]
    batch_size: int
    metrics_port_start: int
    gpu_ids: List[int] | None = Field(default=None)


class NodeCfg(BaseModel):
    node_id: str
    tasks: Dict[str, TaskCfg]


def _load_yaml(path: Path) -> dict:
    return yaml.safe_load(path.read_text())


def load_master_cfg(path: Path = Path("configs/master.yaml")) -> MasterCfg:
    return MasterCfg.model_validate(_load_yaml(path))


def load_node_cfg(path: Path = Path("configs/node.yaml")) -> NodeCfg:
    return NodeCfg.model_validate(_load_yaml(path))