# src/dataprepper/main.py
"""
Main entry point for the dataprepper application.
Provides CLI commands for running master and worker nodes.
"""
import asyncio

import typer
from loguru import logger

from .metrics import MetricsServer
from .mainloop import run_main
from .consumer import launch_node
from .writer import run_writers

# CLI application
app = typer.Typer(
    add_completion=False,
    help="DataPrepper - Data processing pipeline for MT training"
)


class ApplicationRunner:
    """Handles application lifecycle and setup."""

    def __init__(self, metrics_port: int = 8000):
        self.metrics_port = metrics_port
        self.metrics_server = MetricsServer()

    async def run_master(self) -> None:
        """Run the master data processing pipeline."""
        self._setup_metrics()
        logger.info("MAIN starting …")

        try:
            await run_main()
            logger.info("MAIN completed successfully")
        except Exception as e:
            logger.error(f"MAIN failed: {e}")
            raise

    def run_worker_node(self) -> None:
        """Run the worker node."""
        logger.info("NODE starting …")

        try:
            launch_node()
            logger.info("NODE completed successfully")
        except Exception as e:
            logger.error(f"NODE failed: {e}")
            raise

    def _setup_metrics(self) -> None:
        """Initialize metrics server."""
        self.metrics_server.start(self.metrics_port)
        logger.info(f"Metrics server started on port {self.metrics_port}")


# CLI Commands
@app.command()
def main(
    metrics_port: int = typer.Option(
        8000,
        "--metrics-port",
        help="Port for Prometheus metrics server"
    )
) -> None:
    """Run the master data processing pipeline."""
    runner = ApplicationRunner(metrics_port)
    asyncio.run(runner.run_master())


@app.command()
def node() -> None:
    """Run a worker node to process data."""
    runner = ApplicationRunner()
    runner.run_worker_node()


@app.command()
def writer() -> None:
    """Run writer processes to consume output and write to files."""
    logger.info("Starting writer processes")
    asyncio.run(run_writers())


# Entry point
def cli_main() -> None:
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    cli_main()