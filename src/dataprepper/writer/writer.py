# src/dataprepper/writer/writer.py
"""
Writer implementation for consuming processed results and writing to files.
"""
import asyncio
import time
from collections import defaultdict
from typing import Dict, List, Any

from loguru import logger

from ..broker import Broker, StreamMessage
from ..conf import TaskMeta, load_master_cfg
from .formatters import create_formatter, OutputFormatter


class WriterConfig:
    """Configuration for a writer instance."""
    
    def __init__(self, task_name: str, task_meta: TaskMeta, src_lang: str, tgt_lang: str):
        self.task_name = task_name
        self.task_meta = task_meta
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.output_stream = task_meta.output_stream
        self.batch_size = 100  # Number of results to batch before writing
        self.consumer_group = "writers"
        self.consumer_name = f"writer-{task_name}"
        
        logger.info(f"Writer config created for task {task_name}, format: {task_meta.io_format}")


class Writer:
    """A writer that consumes processed results and writes them to files."""
    
    def __init__(self, config: WriterConfig):
        self.config = config
        self.broker = Broker()
        self.formatter = self._create_formatter()
        self.result_buffer: List[Dict[str, Any]] = []
        self._running = False
        self._last_message_time = time.time()

        # Load timeout from master configuration
        master_cfg = load_master_cfg()
        self._idle_timeout = master_cfg.timeouts.writer_idle_timeout

        logger.info(f"Writer created for task: {config.task_name} (idle timeout: {self._idle_timeout}s)")
    
    def _create_formatter(self) -> OutputFormatter:
        """Create appropriate formatter based on task configuration."""
        return create_formatter(
            io_format=self.config.task_meta.io_format,
            output_prefix=self.config.task_meta.output_prefix,
            src_lang=self.config.src_lang,
            tgt_lang=self.config.tgt_lang
        )
    
    async def initialize(self) -> None:
        """Initialize the writer by setting up consumer group."""
        await self._setup_consumer_group()
        logger.info(f"Writer initialized for task: {self.config.task_name}")
    
    async def run(self) -> None:
        """Main writer loop - consumes results and writes to files."""
        await self.initialize()
        self._running = True
        self._last_message_time = time.time()

        logger.info(f"Starting writer loop for task: {self.config.task_name} (will auto-stop after {self._idle_timeout}s of inactivity)")

        try:
            while self._running:
                # Check for idle timeout
                if self._check_idle_timeout():
                    logger.info(f"Writer for task {self.config.task_name} stopping due to {self._idle_timeout}s idle timeout")
                    break

                await self._process_result_batch()
                # Small sleep to prevent busy waiting
                await asyncio.sleep(0.1)
        finally:
            # Write any remaining buffered results
            await self._flush_buffer()
            self.formatter.close()
            logger.info(f"Writer loop stopped for task: {self.config.task_name}")
    
    def stop(self) -> None:
        """Stop the writer loop."""
        logger.info(f"Stopping writer for task: {self.config.task_name}")
        self._running = False

    def _check_idle_timeout(self) -> bool:
        """Check if the writer has been idle for too long."""
        current_time = time.time()
        idle_time = current_time - self._last_message_time

        if idle_time >= self._idle_timeout:
            logger.info(f"Writer for task {self.config.task_name} has been idle for {idle_time:.1f}s (timeout: {self._idle_timeout}s)")
            return True

        # Log idle status every minute
        if int(idle_time) % 60 == 0 and idle_time > 0:
            logger.debug(f"Writer for task {self.config.task_name} idle for {idle_time:.0f}s")

        return False
    
    async def _setup_consumer_group(self) -> None:
        """Set up the consumer group for this writer."""
        logger.info(f"Setting up consumer group '{self.config.consumer_group}' for stream '{self.config.output_stream}'")
        success = await self.broker.create_consumer_group(
            self.config.output_stream,
            self.config.consumer_group
        )
        if success:
            logger.info(f"Created consumer group: {self.config.consumer_group}")
        else:
            logger.info(f"Consumer group already exists: {self.config.consumer_group}")
    
    async def _process_result_batch(self) -> None:
        """Process a batch of results from the output stream."""
        logger.debug(f"Fetching results for task {self.config.task_name}")
        messages = await self._fetch_results()

        if not messages:
            logger.debug(f"No results available for task {self.config.task_name}")
            return

        # Reset idle timer when messages are received
        self._last_message_time = time.time()

        logger.info(f"Received {len(messages)} results for task {self.config.task_name}")

        # Filter results for this specific task and add to buffer
        task_results = []
        ack_ids = []

        for message in messages:
            result = message.data
            if result.get('task') == self.config.task_name:
                task_results.append(result)
            ack_ids.append(message.id)

        if task_results:
            self.result_buffer.extend(task_results)
            logger.debug(f"Added {len(task_results)} results to buffer (total: {len(self.result_buffer)})")

        # Write buffer if it's large enough
        if len(self.result_buffer) >= self.config.batch_size:
            await self._flush_buffer()

        # Acknowledge all messages (even if not for this task)
        await self._acknowledge_messages(ack_ids)
    
    async def _fetch_results(self) -> List[StreamMessage]:
        """Fetch results from the output stream."""
        try:
            messages = await self.broker.pull_messages(
                group=self.config.consumer_group,
                consumer=self.config.consumer_name,
                stream=self.config.output_stream,
                count=self.config.batch_size,
                block_ms=1000,  # Block for 1 second if no messages
            )
            if messages:
                logger.debug(f"Fetched {len(messages)} messages from output stream")
            return messages
        except Exception as e:
            logger.error(f"Error fetching results: {e}")
            return []
    
    async def _flush_buffer(self) -> None:
        """Write buffered results to files."""
        if not self.result_buffer:
            return
        
        try:
            logger.info(f"Writing {len(self.result_buffer)} results for task {self.config.task_name}")
            self.formatter.write_results(self.result_buffer)
            self.result_buffer.clear()
            logger.debug(f"Successfully wrote and cleared buffer for task {self.config.task_name}")
        except Exception as e:
            logger.error(f"Error writing results for task {self.config.task_name}: {e}")
            raise
    
    async def _acknowledge_messages(self, message_ids: List[str]) -> None:
        """Acknowledge processed messages."""
        if not message_ids:
            return
        
        try:
            ack_count = await self.broker.acknowledge_messages(
                self.config.output_stream,
                self.config.consumer_group,
                message_ids
            )
            logger.debug(f"Acknowledged {ack_count} messages")
        except Exception as e:
            logger.error(f"Error acknowledging messages: {e}")
            raise
