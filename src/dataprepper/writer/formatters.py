# src/dataprepper/writer/formatters.py
"""
Output formatters for different file formats.
"""
import csv
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Any, TextIO

from loguru import logger


class OutputFormatter(ABC):
    """Base class for output formatters."""
    
    def __init__(self, output_prefix: str):
        self.output_prefix = Path(output_prefix)
        self.output_prefix.parent.mkdir(parents=True, exist_ok=True)
    
    @abstractmethod
    def write_results(self, results: List[Dict[str, Any]]) -> None:
        """Write results to output files."""
        pass
    
    @abstractmethod
    def close(self) -> None:
        """Close any open file handles."""
        pass


class CSVFormatter(OutputFormatter):
    """Formatter for CSV output."""
    
    def __init__(self, output_prefix: str):
        super().__init__(output_prefix)
        self.csv_file = None
        self.csv_writer = None
        self.headers_written = False
        self._open_csv_file()
    
    def _open_csv_file(self) -> None:
        """Open CSV file for writing."""
        csv_path = self.output_prefix.with_suffix('.csv')
        logger.info(f"Opening CSV file for writing: {csv_path}")
        self.csv_file = open(csv_path, 'w', newline='', encoding='utf-8')
        self.csv_writer = csv.writer(self.csv_file)
    
    def write_results(self, results: List[Dict[str, Any]]) -> None:
        """Write results to CSV file."""
        if not results:
            return
        
        # Write headers if not already written
        if not self.headers_written:
            headers = list(results[0].keys())
            self.csv_writer.writerow(headers)
            self.headers_written = True
            logger.debug(f"Wrote CSV headers: {headers}")
        
        # Write data rows
        for result in results:
            row = [result.get(key, '') for key in results[0].keys()]
            self.csv_writer.writerow(row)
        
        self.csv_file.flush()
        logger.debug(f"Wrote {len(results)} rows to CSV")
    
    def close(self) -> None:
        """Close CSV file."""
        if self.csv_file:
            self.csv_file.close()
            logger.info(f"Closed CSV file: {self.output_prefix.with_suffix('.csv')}")


class ParallelFormatter(OutputFormatter):
    """Formatter for parallel text output."""
    
    def __init__(self, output_prefix: str, src_lang: str, tgt_lang: str):
        super().__init__(output_prefix)
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.src_file = None
        self.tgt_file = None
        self._open_parallel_files()
    
    def _open_parallel_files(self) -> None:
        """Open parallel text files for writing."""
        src_path = self.output_prefix.with_suffix(f'.{self.src_lang}')
        tgt_path = self.output_prefix.with_suffix(f'.{self.tgt_lang}')
        
        logger.info(f"Opening parallel files: {src_path}, {tgt_path}")
        self.src_file = open(src_path, 'w', encoding='utf-8')
        self.tgt_file = open(tgt_path, 'w', encoding='utf-8')
    
    def write_results(self, results: List[Dict[str, Any]]) -> None:
        """Write results to parallel text files."""
        if not results:
            return
        
        # Sort by sentence_id to maintain order
        sorted_results = sorted(results, key=lambda x: int(x.get('sentence_id', 0)))
        
        for result in sorted_results:
            src_text = result.get('src', '')
            tgt_text = result.get('tgt', '')
            
            self.src_file.write(f"{src_text}\n")
            self.tgt_file.write(f"{tgt_text}\n")
        
        self.src_file.flush()
        self.tgt_file.flush()
        logger.debug(f"Wrote {len(results)} lines to parallel files")
    
    def close(self) -> None:
        """Close parallel text files."""
        if self.src_file:
            self.src_file.close()
            logger.info(f"Closed source file: {self.output_prefix.with_suffix(f'.{self.src_lang}')}")
        if self.tgt_file:
            self.tgt_file.close()
            logger.info(f"Closed target file: {self.output_prefix.with_suffix(f'.{self.tgt_lang}')}")


class CombinedFormatter(OutputFormatter):
    """Formatter that combines CSV and parallel text output."""
    
    def __init__(self, output_prefix: str, src_lang: str, tgt_lang: str):
        super().__init__(output_prefix)
        self.csv_formatter = CSVFormatter(str(output_prefix))
        self.parallel_formatter = ParallelFormatter(str(output_prefix), src_lang, tgt_lang)
    
    def write_results(self, results: List[Dict[str, Any]]) -> None:
        """Write results to both CSV and parallel text files."""
        self.csv_formatter.write_results(results)
        self.parallel_formatter.write_results(results)
    
    def close(self) -> None:
        """Close all formatters."""
        self.csv_formatter.close()
        self.parallel_formatter.close()


def create_formatter(io_format: str, output_prefix: str, src_lang: str = "en", tgt_lang: str = "es") -> OutputFormatter:
    """Factory function to create appropriate formatter based on io_format."""
    if io_format == "csv":
        return CSVFormatter(output_prefix)
    elif io_format == "parallel":
        return ParallelFormatter(output_prefix, src_lang, tgt_lang)
    elif io_format == "parallel+csv":
        return CombinedFormatter(output_prefix, src_lang, tgt_lang)
    else:
        raise ValueError(f"Unsupported io_format: {io_format}")
