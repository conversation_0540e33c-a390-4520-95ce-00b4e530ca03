# src/dataprepper/writer/manager.py
"""
Writer manager for coordinating multiple writers.
"""
import asyncio
from typing import Dict, List

from loguru import logger

from ..conf import load_master_cfg, MasterCfg, TaskMeta
from .writer import Writer, WriterConfig


class WriterManager:
    """Manages multiple writers for different tasks."""
    
    def __init__(self):
        self.config = load_master_cfg()
        self.writers: Dict[str, Writer] = {}
        self._running = False
        logger.info("Writer manager initialized")
    
    async def start_all_writers(self) -> None:
        """Start writers for all enabled tasks."""
        enabled_tasks = self._get_enabled_tasks()
        
        if not enabled_tasks:
            logger.warning("No enabled tasks found for writers")
            return
        
        logger.info(f"Starting writers for {len(enabled_tasks)} tasks: {list(enabled_tasks.keys())}")
        
        # Create writers for each task
        for task_name, task_meta in enabled_tasks.items():
            writer_config = WriterConfig(
                task_name=task_name,
                task_meta=task_meta,
                src_lang=self.config.src_lang,
                tgt_lang=self.config.tgt_lang
            )
            writer = Writer(writer_config)
            self.writers[task_name] = writer
        
        # Start all writers concurrently
        self._running = True
        writer_tasks = [
            asyncio.create_task(writer.run(), name=f"writer-{task_name}")
            for task_name, writer in self.writers.items()
        ]
        
        try:
            logger.info("All writers started, waiting for completion...")
            await asyncio.gather(*writer_tasks)
        except Exception as e:
            logger.error(f"Error in writer manager: {e}")
            await self.stop_all_writers()
            raise
    
    async def stop_all_writers(self) -> None:
        """Stop all running writers."""
        if not self._running:
            return
        
        logger.info("Stopping all writers...")
        self._running = False
        
        for task_name, writer in self.writers.items():
            logger.info(f"Stopping writer for task: {task_name}")
            writer.stop()
        
        # Give writers time to finish current operations
        await asyncio.sleep(1)
        logger.info("All writers stopped")
    
    def _get_enabled_tasks(self) -> Dict[str, TaskMeta]:
        """Get all enabled tasks from configuration."""
        return {
            name: meta 
            for name, meta in self.config.tasks.items() 
            if meta.enabled
        }
    
    async def run_until_stream_empty(self, check_interval: float = 5.0, empty_timeout: float = None) -> None:
        """
        Run writers until the output stream is empty and stays empty for the specified timeout.
        This is useful for batch processing scenarios.

        Args:
            check_interval: How often to check the stream (seconds)
            empty_timeout: How long the stream must be empty before stopping (seconds, defaults to config value)
        """
        from ..broker import Broker
        import time

        # Use configured timeout if not specified
        if empty_timeout is None:
            empty_timeout = self.config.timeouts.writer_idle_timeout

        broker = Broker()
        output_stream = "mt:active:out"  # Default output stream
        empty_since = None  # Track when the stream first became empty

        logger.info(f"Running writers until output stream is empty for {empty_timeout}s (checking every {check_interval}s)")

        # Start writers in background
        writer_task = asyncio.create_task(self.start_all_writers())

        try:
            while True:
                stream_length = await broker.get_stream_length(output_stream)
                current_time = time.time()

                if stream_length == 0:
                    if empty_since is None:
                        empty_since = current_time
                        logger.info("Output stream is now empty, starting timeout countdown")

                    empty_duration = current_time - empty_since
                    logger.debug(f"Output stream empty for {empty_duration:.1f}s (timeout: {empty_timeout}s)")

                    if empty_duration >= empty_timeout:
                        logger.info(f"Output stream has been empty for {empty_duration:.1f}s, stopping writers")
                        break
                else:
                    if empty_since is not None:
                        logger.info(f"Output stream has {stream_length} messages, resetting empty timer")
                    empty_since = None
                    logger.debug(f"Output stream has {stream_length} messages")

                await asyncio.sleep(check_interval)

        finally:
            await self.stop_all_writers()

            # Cancel the writer task if it's still running
            if not writer_task.done():
                writer_task.cancel()
                try:
                    await writer_task
                except asyncio.CancelledError:
                    pass


# Public API function
async def run_writers() -> None:
    """Main entry point for running writers."""
    manager = WriterManager()
    await manager.run_until_stream_empty()
