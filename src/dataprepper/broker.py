# src/dataprepper/broker.py
from __future__ import annotations

from typing import List, Op<PERSON>, Tuple

from loguru import logger
from redis.asyncio import Redis


class StreamMessage:
    def __init__(self, message_id: str, data: dict):
        self.id = message_id
        self.data = data


class Broker:

    def __init__(self, url: str = "redis://localhost:6379/0"):
        self._redis: Redis = Redis.from_url(url, decode_responses=True)


    async def get_stream_length(self, stream: str) -> int:
        """Get the number of messages in a stream."""
        length = await self._redis.xlen(stream)
        logger.debug(f"Stream {stream} has {length} messages")
        return length

    async def create_consumer_group(
        self,
        stream: str,
        group: str,
        start_id: str = "$"
    ) -> bool:
        """
        Create a consumer group for a stream.
        Returns True if created, False if already exists.
        """
        try:
            await self._redis.xgroup_create(stream, group, start_id, mkstream=True)
            logger.info(f"Created consumer group '{group}' for stream '{stream}'")
            return True
        except Exception as e:
            logger.debug(f"Consumer group '{group}' already exists for stream '{stream}': {e}")
            return False


    # Producer Operations
    async def push_messages(self, stream: str, messages: List[dict]) -> None:
        if not messages:
            return
        await self._batch_add_messages(stream, messages)


    async def push_message(self, stream: str, message: dict) -> str:
        return await self._redis.xadd(stream, message)

    async def _batch_add_messages(self, stream: str, messages: List[dict]) -> None:
        pipe = self._redis.pipeline()
        for msg in messages:
            pipe.xadd(stream, msg)
        await pipe.execute()


    async def pull_messages(
        self,
        group: str,
        consumer: str,
        stream: str,
        count: int,
        block_ms: int = 0,
    ) -> List[StreamMessage]:
        logger.debug(f"Pulling messages: group={group}, consumer={consumer}, stream={stream}, count={count}, block_ms={block_ms}")

        raw_result = await self._redis.xreadgroup(
            groupname=group,
            consumername=consumer,
            streams={stream: ">"},
            count=count,
            block=block_ms,
        )

        messages = self._parse_stream_messages(raw_result)
        logger.debug(f"Pulled {len(messages)} messages from stream '{stream}'")
        return messages

    async def acknowledge_messages(
        self,
        stream: str,
        group: str,
        message_ids: List[str]
    ) -> int:
        if not message_ids:
            return 0
        return await self._redis.xack(stream, group, *message_ids)

    def _parse_stream_messages(self, raw_result: list) -> List[StreamMessage]:
        if not raw_result:
            return []

        # raw_result format: [(stream_name, [(msg_id, msg_data), ...])]
        _, messages = raw_result[0]
        return [StreamMessage(msg_id, msg_data) for msg_id, msg_data in messages]


    async def pull(
        self,
        group: str,
        consumer: str,
        stream: str,
        count: int,
        block_ms: int = 0,
    ) -> list:
        """
        Legacy method - use pull_messages instead.
        Returns raw Redis format for backward compatibility.
        """
        return await self._redis.xreadgroup(
            groupname=group,
            consumername=consumer,
            streams={stream: ">"},
            count=count,
            block=block_ms,
        )

    @property
    def r(self) -> Redis:
        """Legacy property for direct Redis access - use specific methods instead."""
        return self._redis