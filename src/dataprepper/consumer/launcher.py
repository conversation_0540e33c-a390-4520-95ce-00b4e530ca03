# src/dataprepper/consumer/launcher.py
"""
Node launcher for managing worker processes.
"""
import asyncio
import multiprocessing as mp
from typing import List, Dict, <PERSON><PERSON>

from loguru import logger

from ..conf import load_master_cfg, load_node_cfg, TaskMeta, TaskCfg, NodeCfg, MasterCfg
from ..metrics import boot_metrics
from .worker import run_worker


class TaskFilter:
    """Filters tasks based on configuration and enabled status."""
    
    @staticmethod
    def get_runnable_tasks(
        master_cfg: MasterCfg, 
        node_cfg: NodeCfg
    ) -> Dict[str, Tuple[TaskMeta, TaskCfg]]:
        """
        Get tasks that should run on this node.
        Returns dict of task_name -> (master_task_meta, node_task_cfg).
        """
        runnable_tasks = {}
        
        for task_name, master_task in master_cfg.tasks.items():
            if not TaskFilter._should_run_task(task_name, master_task, node_cfg):
                continue
            
            node_task = node_cfg.tasks[task_name]
            runnable_tasks[task_name] = (master_task, node_task)
        
        logger.info(f"Found {len(runnable_tasks)} runnable tasks: {list(runnable_tasks.keys())}")
        return runnable_tasks
    
    @staticmethod
    def _should_run_task(task_name: str, master_task: TaskMeta, node_cfg: NodeCfg) -> bool:
        """Check if a task should run on this node."""
        if not master_task.enabled:
            logger.debug(f"Task {task_name} disabled in master config")
            return False
        
        if task_name not in node_cfg.tasks:
            logger.debug(f"Task {task_name} not configured for this node")
            return False
        
        node_task = node_cfg.tasks[task_name]
        if not node_task.enabled:
            logger.debug(f"Task {task_name} disabled in node config")
            return False
        
        return True


class WorkerProcess:
    """Represents a single worker process."""
    
    def __init__(self, task: str, node_cfg: NodeCfg, group: str, port: int):
        self.task = task
        self.node_cfg = node_cfg
        self.group = group
        self.port = port
        self.process: mp.Process = None
    
    def start(self) -> None:
        """Start the worker process."""
        logger.info(f"Starting worker process for task {self.task} on port {self.port}")
        self.process = mp.Process(
            target=self._worker_entry_point,
            args=(self.task, self.node_cfg, self.group, self.port)
        )
        self.process.start()
        
        task_cfg = self.node_cfg.tasks[self.task]
        logger.info(
            f"  worker pid={self.process.pid} port={self.port} "
            f"device={task_cfg.worker_type}"
        )
    
    def join(self) -> None:
        """Wait for the worker process to complete."""
        if self.process:
            logger.debug(f"Waiting for worker process {self.process.pid} to complete")
            self.process.join()
    
    @staticmethod
    def _worker_entry_point(task: str, node_cfg: NodeCfg, group: str, port: int) -> None:
        """Entry point for worker process."""
        logger.info(f"Worker process started for task {task} on port {port}")
        boot_metrics(port)
        asyncio.run(run_worker(task, node_cfg, group))


class TaskRunner:
    """Manages running a single task with multiple worker processes."""
    
    def __init__(self, task_name: str, task_cfg: TaskCfg, node_cfg: NodeCfg, group: str):
        self.task_name = task_name
        self.task_cfg = task_cfg
        self.node_cfg = node_cfg
        self.group = group
        self.workers: List[WorkerProcess] = []
    
    def start_workers(self) -> None:
        """Start all worker processes for this task."""
        logger.info(f"⏩ starting stage {self.task_name} with {self.task_cfg.num_workers} workers")
        
        for i in range(self.task_cfg.num_workers):
            port = self.task_cfg.metrics_port_start + i
            worker = WorkerProcess(self.task_name, self.node_cfg, self.group, port)
            worker.start()
            self.workers.append(worker)
    
    def wait_for_completion(self) -> None:
        """Wait for all worker processes to complete."""
        logger.info(f"Waiting for {len(self.workers)} workers to complete for task {self.task_name}")
        for worker in self.workers:
            worker.join()
        
        logger.info(f"✔ finished stage {self.task_name}")


class NodeLauncher:
    """Manages launching and coordinating all tasks on a node."""
    
    def __init__(self):
        self.node_cfg = load_node_cfg()
        self.master_cfg = load_master_cfg()
        self.group = "cg1"  # Consumer group name
        logger.info(f"Node launcher initialized for node: {self.node_cfg.node_id}")
    
    def launch_all_tasks(self) -> None:
        """Launch all runnable tasks on this node."""
        runnable_tasks = TaskFilter.get_runnable_tasks(self.master_cfg, self.node_cfg)
        
        if not runnable_tasks:
            logger.warning("No runnable tasks found for this node")
            return
        
        for task_name, (master_task, node_task) in runnable_tasks.items():
            logger.info(f"Launching task: {task_name}")
            self._run_single_task(task_name, node_task)
    
    def _run_single_task(self, task_name: str, task_cfg: TaskCfg) -> None:
        """Run a single task with its configured workers."""
        task_runner = TaskRunner(task_name, task_cfg, self.node_cfg, self.group)
        task_runner.start_workers()
        task_runner.wait_for_completion()


# Public API
def launch_node() -> None:
    """Main entry point for launching worker node."""
    logger.info("Starting node launcher")
    launcher = NodeLauncher()
    launcher.launch_all_tasks()


# Legacy function for backward compatibility
def _proc(task, node_cfg, group, port):
    """Legacy function - use WorkerProcess._worker_entry_point instead."""
    boot_metrics(port)
    asyncio.run(run_worker(task, node_cfg, group))
