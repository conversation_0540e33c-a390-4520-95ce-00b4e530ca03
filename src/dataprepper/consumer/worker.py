# src/dataprepper/consumer/worker.py
"""
Worker implementation for consuming and processing messages from Redis streams.
"""
import asyncio
import time
from typing import List

from loguru import logger

from ..broker import Broker, StreamMessage
from ..conf import load_master_cfg
from .processor import MessageProcessor, WorkerConfig


class Worker:
    """A worker that processes messages from a Redis stream."""

    def __init__(self, config: WorkerConfig):
        self.config = config
        self.broker = Broker()
        self.processor = MessageProcessor(config.task, config.node_cfg.node_id)
        self.input_stream = "mt:active:in"
        self.output_stream = "mt:active:out"
        self._running = False
        self._last_message_time = time.time()

        # Load timeout from master configuration
        master_cfg = load_master_cfg()
        self._idle_timeout = master_cfg.timeouts.worker_idle_timeout

        logger.info(f"Worker created for task: {config.task} (idle timeout: {self._idle_timeout}s)")
    
    async def initialize(self) -> None:
        """Initialize the worker by setting up consumer group."""
        await self._setup_consumer_group()
        logger.info(f"Worker initialized for task: {self.config.task}")
    
    async def run(self) -> None:
        """Main worker loop - processes messages continuously."""
        await self.initialize()
        self._running = True
        self._last_message_time = time.time()

        logger.info(f"Starting worker loop for task: {self.config.task} (will auto-stop after {self._idle_timeout}s of inactivity)")

        while self._running:
            try:
                # Check for idle timeout
                if self._check_idle_timeout():
                    logger.info(f"Worker for task {self.config.task} stopping due to {self._idle_timeout}s idle timeout")
                    break

                await self._process_message_batch()
                # Small sleep to prevent busy waiting when no messages
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Error in worker loop for task {self.config.task}: {e}")
                await asyncio.sleep(1)  # Brief pause before retrying

        logger.info(f"Worker loop stopped for task: {self.config.task}")
    
    def stop(self) -> None:
        """Stop the worker loop."""
        logger.info(f"Stopping worker for task: {self.config.task}")
        self._running = False

    def _check_idle_timeout(self) -> bool:
        """Check if the worker has been idle for too long."""
        current_time = time.time()
        idle_time = current_time - self._last_message_time

        if idle_time >= self._idle_timeout:
            logger.info(f"Worker for task {self.config.task} has been idle for {idle_time:.1f}s (timeout: {self._idle_timeout}s)")
            return True

        # Log idle status every minute
        if int(idle_time) % 60 == 0 and idle_time > 0:
            logger.debug(f"Worker for task {self.config.task} idle for {idle_time:.0f}s")

        return False
    
    async def _setup_consumer_group(self) -> None:
        """Set up the consumer group for this worker."""
        logger.info(f"Setting up consumer group '{self.config.group}' for stream '{self.input_stream}'")
        success = await self.broker.create_consumer_group(
            self.input_stream, 
            self.config.group
        )
        if success:
            logger.info(f"Created consumer group: {self.config.group}")
        else:
            logger.info(f"Consumer group already exists: {self.config.group}")
    
    async def _process_message_batch(self) -> None:
        """Process a single batch of messages."""
        logger.debug(f"Fetching messages for task {self.config.task}")
        messages = await self._fetch_messages()

        if not messages:
            logger.debug(f"No messages available for task {self.config.task}")
            return

        # Reset idle timer when messages are received
        self._last_message_time = time.time()

        logger.info(f"Received {len(messages)} messages for task {self.config.task}")
        results, ack_ids = self.processor.process_batch(messages)

        # Send results and acknowledge messages
        await self._send_results(results)
        await self._acknowledge_messages(ack_ids)
    
    async def _fetch_messages(self) -> List[StreamMessage]:
        """Fetch a batch of messages from the input stream."""
        try:
            messages = await self.broker.pull_messages(
                group=self.config.group,
                consumer=self.config.consumer_name,
                stream=self.input_stream,
                count=self.config.batch_size,
                block_ms=1000,  # Block for 1 second if no messages
            )
            if messages:
                logger.debug(f"Fetched {len(messages)} messages from stream")
            return messages
        except Exception as e:
            logger.error(f"Error fetching messages: {e}")
            return []
    
    async def _send_results(self, results: List[dict]) -> None:
        """Send processed results to the output stream."""
        if not results:
            return
        
        try:
            logger.info(f"Sending {len(results)} results to output stream")
            await self.broker.push_messages(self.output_stream, results)
            logger.debug(f"Successfully sent {len(results)} results")
        except Exception as e:
            logger.error(f"Error sending results: {e}")
            raise
    
    async def _acknowledge_messages(self, message_ids: List[str]) -> None:
        """Acknowledge processed messages."""
        if not message_ids:
            return
        
        try:
            ack_count = await self.broker.acknowledge_messages(
                self.input_stream, 
                self.config.group, 
                message_ids
            )
            logger.debug(f"Acknowledged {ack_count} messages")
        except Exception as e:
            logger.error(f"Error acknowledging messages: {e}")
            raise


# Public API function for backward compatibility
async def run_worker(task: str, node_cfg, group: str) -> None:
    """
    Main entry point for running a worker.
    One asyncio loop per *process*; you still launch N processes
    according to node_cfg.tasks[task].num_workers.
    """
    config = WorkerConfig(task, node_cfg, group)
    worker = Worker(config)
    await worker.run()
