# src/dataprepper/consumer/processor.py
"""
Message processing logic for consumer workers.
"""
from typing import Callable, List, Dict, Any, Tuple

from loguru import logger

from ..broker import StreamMessage
from ..metrics import MSG_PROCESSED, PROCESSING_ERRORS
from ..tasks import get as get_processor


class MessageProcessor:
    """Handles processing of individual messages."""
    
    def __init__(self, task: str, node_id: str):
        self.task = task
        self.node_id = node_id
        self.processor: Callable[[dict], dict] = get_processor(task)
        logger.info(f"Initialized processor for task: {task}")
    
    def process_message(self, message_data: dict) -> dict:
        """Process a single message and return the result."""
        try:
            logger.debug(f"Processing message for task {self.task}: {message_data}")
            result = self.processor(message_data)
            result["task"] = self.task
            logger.debug(f"Processed message result: {result}")
            return result
        except Exception as e:
            logger.error(f"Error processing message for task {self.task}: {e}")
            PROCESSING_ERRORS.labels(task=self.task, error_type=type(e).__name__).inc()
            raise
    
    def process_batch(self, messages: List[StreamMessage]) -> Tuple[List[dict], List[str]]:
        """
        Process a batch of messages.
        Returns (results, message_ids_to_ack).
        """
        if not messages:
            return [], []
        
        logger.info(f"Processing batch of {len(messages)} messages for task {self.task}")
        results = []
        ack_ids = []
        
        for message in messages:
            try:
                result = self.process_message(message.data)
                results.append(result)
                ack_ids.append(message.id)
                
                # Update metrics
                MSG_PROCESSED.labels(task=self.task, node_id=self.node_id).inc()
                
            except Exception as e:
                logger.error(f"Failed to process message {message.id}: {e}")
                # Continue processing other messages in the batch
                continue
        
        logger.info(f"Successfully processed {len(results)} out of {len(messages)} messages")
        return results, ack_ids


class WorkerConfig:
    """Configuration for a worker instance."""

    def __init__(self, task: str, node_cfg, group: str):
        self.task = task
        self.node_cfg = node_cfg
        self.group = group
        self.task_cfg = node_cfg.tasks[task]
        self.batch_size = self.task_cfg.batch_size
        self.consumer_name = self._generate_consumer_name()
        logger.info(f"Worker config created for task {task}, batch_size={self.batch_size}")

    def _generate_consumer_name(self) -> str:
        """Generate a unique consumer name for this worker."""
        import asyncio
        task_id = id(asyncio.current_task())
        consumer_name = f"{self.node_cfg.node_id}-{self.task}-{task_id}"
        logger.debug(f"Generated consumer name: {consumer_name}")
        return consumer_name
