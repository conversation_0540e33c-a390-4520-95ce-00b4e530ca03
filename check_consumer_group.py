#!/usr/bin/env python3
"""
Check the status of consumer groups and pending messages.
"""
import asyncio
import sys
import redis.asyncio as redis

async def check_consumer_group_status():
    """Check the status of the consumer group."""
    r = redis.Redis.from_url("redis://localhost:6379/0", decode_responses=True)
    
    stream = "mt:active:in"
    group = "cg1"
    
    print("=== Consumer Group Status ===")
    
    try:
        # Get stream length
        stream_length = await r.xlen(stream)
        print(f"Stream length: {stream_length}")
        
        # Get consumer group info
        groups = await r.xinfo_groups(stream)
        print(f"Consumer groups: {len(groups)}")
        
        for group_info in groups:
            print(f"  Group: {group_info['name']}")
            print(f"  Consumers: {group_info['consumers']}")
            print(f"  Pending: {group_info['pending']}")
            print(f"  Last delivered ID: {group_info['last-delivered-id']}")
            
            # Get consumer info for this group
            try:
                consumers = await r.xinfo_consumers(stream, group_info['name'])
                print(f"  Active consumers: {len(consumers)}")
                for consumer in consumers:
                    print(f"    Consumer: {consumer['name']}")
                    print(f"    Pending: {consumer['pending']}")
                    print(f"    Idle: {consumer['idle']} ms")
            except Exception as e:
                print(f"  Error getting consumer info: {e}")
        
        # Check pending messages
        try:
            pending = await r.xpending(stream, group)
            if pending:
                print(f"Pending messages summary: {pending}")
                
                # Get detailed pending info
                pending_details = await r.xpending_range(stream, group, "-", "+", 10)
                print(f"Pending message details (first 10):")
                for detail in pending_details:
                    print(f"  ID: {detail['message_id']}, Consumer: {detail['consumer']}, Elapsed: {detail['time_since_delivered']} ms")
        except Exception as e:
            print(f"Error checking pending messages: {e}")
            
    except Exception as e:
        print(f"Error: {e}")
    
    await r.close()

if __name__ == "__main__":
    asyncio.run(check_consumer_group_status())
